/**
 * 任务状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type {
  Task,
  TaskCreateRequest,
  TaskUpdateRequest,
  TaskFilter,
  TaskStatistics,
  TaskComment,
  TaskAttachment,
  TaskStatus,
  TaskPriority,
  TaskType
} from '@/types/task'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const stats = ref<TaskStatistics | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 分页和筛选
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    hasNext: false,
    hasPrevious: false
  })

  const filters = ref<TaskFilter>({})

  // 计算属性
  const todoTasks = computed(() =>
    tasks.value.filter(task => task.status === 'todo')
  )

  const inProgressTasks = computed(() =>
    tasks.value.filter(task => task.status === 'in_progress')
  )

  const completedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'completed')
  )

  const overdueTasks = computed(() =>
    tasks.value.filter(task => {
      if (!task.due_date || task.status === 'completed') return false
      return new Date(task.due_date) < new Date()
    })
  )

  const tasksByPriority = computed(() => {
    return tasks.value.reduce((acc, task) => {
      if (!acc[task.priority]) {
        acc[task.priority] = []
      }
      acc[task.priority].push(task)
      return acc
    }, {} as Record<string, Task[]>)
  })

  const tasksByProject = computed(() => {
    return tasks.value.reduce((acc, task) => {
      if (!acc[task.project_id]) {
        acc[task.project_id] = []
      }
      acc[task.project_id].push(task)
      return acc
    }, {} as Record<string, Task[]>)
  })

  // 方法
  const fetchTasks = async (params?: any) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用 - 获取任务列表
      const mockTasks: Task[] = [
        {
          id: 'task-1',
          title: '设计系统架构',
          description: '设计整个项目管理系统的技术架构',
          project_id: 'project-1',
          status: 'in_progress',
          priority: 'high',
          type: 'task',
          assignee_id: '1',
          start_date: '2024-01-01',
          due_date: '2024-01-15',
          estimated_hours: 40,
          actual_hours: 20,
          progress: 60,
          tags: ['架构', '设计'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          parent_task_id: undefined,
          is_overdue: false,
          completion_rate: 0.6,
          effort_variance: -20
        },
        {
          id: 'task-2',
          title: '前端界面开发',
          description: '开发用户界面和交互功能',
          project_id: 'project-1',
          status: 'todo',
          priority: 'medium',
          type: 'task',
          assignee_id: '1',
          start_date: '2024-01-16',
          due_date: '2024-02-15',
          estimated_hours: 80,
          actual_hours: 0,
          progress: 0,
          tags: ['前端', 'Vue'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          parent_task_id: undefined,
          is_overdue: false,
          completion_rate: 0,
          effort_variance: 0
        }
      ]

      tasks.value = mockTasks
      pagination.value.total = mockTasks.length

      return mockTasks
    } catch (err: any) {
      error.value = err.message || '获取任务列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTask = async (id: string) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用 - 获取任务详情
      const task = tasks.value.find(t => t.id === id)
      if (task) {
        currentTask.value = task
        return task
      }
      throw new Error('任务不存在')
    } catch (err: any) {
      error.value = err.message || '获取任务详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTask = async (data: TaskCreateRequest) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用 - 创建任务
      const newTask: Task = {
        id: `task-${Date.now()}`,
        title: data.title,
        description: data.description,
        project_id: data.project_id,
        status: data.status || 'todo',
        priority: data.priority || 'medium',
        type: data.type || 'task',
        assignee_id: data.assignee_id,
        start_date: data.start_date,
        due_date: data.due_date,
        estimated_hours: data.estimated_hours,
        actual_hours: 0,
        progress: 0,
        tags: data.tags || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        parent_task_id: data.parent_task_id,
        is_overdue: false,
        completion_rate: 0,
        effort_variance: 0
      }

      tasks.value.unshift(newTask)
      pagination.value.total += 1

      return newTask
    } catch (err: any) {
      error.value = err.message || '创建任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTask = async (id: string, data: Partial<TaskUpdateRequest>) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用 - 更新任务
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value[index] = {
          ...tasks.value[index],
          ...data,
          updated_at: new Date().toISOString()
        }

        // 更新当前任务
        if (currentTask.value?.id === id) {
          currentTask.value = tasks.value[index]
        }

        return tasks.value[index]
      }
      throw new Error('任务不存在')
    } catch (err: any) {
      error.value = err.message || '更新任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteTask = async (id: string) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用 - 删除任务
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value.splice(index, 1)
        pagination.value.total -= 1

        // 清除当前任务
        if (currentTask.value?.id === id) {
          currentTask.value = null
        }
      }
    } catch (err: any) {
      error.value = err.message || '删除任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTaskStatus = async (id: string, status: string) => {
    try {
      await updateTask(id, {
        status,
        completed_date: status === 'completed' ? new Date().toISOString() : null,
        progress: status === 'completed' ? 100 : undefined
      })
    } catch (err: any) {
      error.value = err.message || '更新任务状态失败'
      throw err
    }
  }

  const fetchTasksByProject = async (projectId: string) => {
    try {
      await fetchTasks({ project_id: projectId })
      return tasks.value.filter(task => task.project_id === projectId)
    } catch (err: any) {
      error.value = err.message || '获取项目任务失败'
      throw err
    }
  }

  const setFilters = (newFilters: Partial<TaskFilter>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {}
  }

  const setCurrentTask = (task: Task | null) => {
    currentTask.value = task
  }

  const clearError = () => {
    error.value = null
  }

  // 重置状态
  const reset = () => {
    tasks.value = []
    currentTask.value = null
    stats.value = null
    loading.value = false
    error.value = null
    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0,
      hasNext: false,
      hasPrevious: false
    }
    filters.value = {}
  }

  return {
    // 状态
    tasks: readonly(tasks),
    currentTask: readonly(currentTask),
    stats: readonly(stats),
    loading: readonly(loading),
    error: readonly(error),
    pagination: readonly(pagination),
    filters: readonly(filters),

    // 计算属性
    todoTasks,
    inProgressTasks,
    completedTasks,
    overdueTasks,
    tasksByPriority,
    tasksByProject,

    // 方法
    fetchTasks,
    fetchTask,
    createTask,
    updateTask,
    deleteTask,
    updateTaskStatus,
    fetchTasksByProject,
    setFilters,
    clearFilters,
    setCurrentTask,
    clearError,
    reset
  }
})
