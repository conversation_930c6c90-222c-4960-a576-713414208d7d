import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/utils/axios'
import type {
  Project,
  ProjectStats,
  ProjectCreateRequest,
  ProjectUpdateRequest,
  ProjectFilters,
  ProjectListResponse,
  GanttData
} from '@/types/project'

export const useProjectStore = defineStore('project', () => {
  // 状态
  const projects = ref<Project[]>([])
  const currentProject = ref<Project | null>(null)
  const stats = ref<ProjectStats | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 分页信息
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    hasNext: false,
    hasPrevious: false
  })

  // 过滤条件
  const filters = ref<ProjectFilters>({})

  // 计算属性
  const activeProjects = computed(() =>
    projects.value.filter(p => p.status === 'in_progress')
  )

  const completedProjects = computed(() =>
    projects.value.filter(p => p.status === 'completed')
  )

  const overdueProjects = computed(() =>
    projects.value.filter(p => p.is_overdue)
  )

  const projectsByPriority = computed(() => {
    const groups: Record<string, Project[]> = {
      critical: [],
      high: [],
      medium: [],
      low: []
    }

    projects.value.forEach(project => {
      if (groups[project.priority]) {
        groups[project.priority].push(project)
      }
    })

    return groups
  })

  const totalBudget = computed(() =>
    projects.value.reduce((sum, p) => sum + p.budget, 0)
  )

  const totalSpent = computed(() =>
    projects.value.reduce((sum, p) => sum + p.spent, 0)
  )

  const averageProgress = computed(() => {
    if (projects.value.length === 0) return 0
    const totalProgress = projects.value.reduce((sum, p) => sum + p.progress, 0)
    return Math.round(totalProgress / projects.value.length)
  })

  // 操作方法
  const fetchProjects = async (page = 1, pageSize = 20) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用 - 返回模拟项目数据
      const mockProjects: Project[] = [
        {
          id: 'project-1',
          name: '项目管理系统开发',
          code: 'PRJ-2024-001',
          description: '开发一个完整的项目管理系统，包含项目创建、任务管理、团队协作等功能',
          status: 'in_progress',
          priority: 'high',
          start_date: '2024-01-01',
          end_date: '2024-06-30',
          progress: 65,
          budget: 500000,
          spent: 320000,
          manager_id: '1',
          manager_name: '管理员',
          team_members: [],
          client_name: '科技公司',
          client_contact: '张经理',
          client_email: '<EMAIL>',
          tags: '开发,管理系统',
          category: 'Web开发',
          is_active: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: new Date().toISOString(),
          task_count: 15,
          completed_task_count: 8,
          overdue_task_count: 2,
          team_member_count: 5
        },
        {
          id: 'project-2',
          name: '移动应用开发',
          code: 'PRJ-2024-002',
          description: '开发跨平台移动应用，支持iOS和Android',
          status: 'planning',
          priority: 'medium',
          start_date: '2024-03-01',
          end_date: '2024-09-30',
          progress: 20,
          budget: 300000,
          spent: 50000,
          manager_id: '1',
          manager_name: '管理员',
          team_members: [],
          client_name: '创新科技',
          client_contact: '李总',
          client_email: '<EMAIL>',
          tags: '移动开发,跨平台',
          category: '移动应用',
          is_active: true,
          created_at: '2024-02-15T00:00:00Z',
          updated_at: new Date().toISOString(),
          task_count: 8,
          completed_task_count: 2,
          overdue_task_count: 0,
          team_member_count: 3
        },
        {
          id: 'project-3',
          name: '数据分析平台',
          code: 'PRJ-2024-003',
          description: '构建企业级数据分析和可视化平台',
          status: 'completed',
          priority: 'high',
          start_date: '2023-09-01',
          end_date: '2024-02-29',
          progress: 100,
          budget: 800000,
          spent: 750000,
          manager_id: '1',
          manager_name: '管理员',
          team_members: [],
          client_name: '大数据公司',
          client_contact: '王总监',
          client_email: '<EMAIL>',
          tags: '数据分析,可视化',
          category: '数据平台',
          is_active: true,
          created_at: '2023-09-01T00:00:00Z',
          updated_at: '2024-02-29T00:00:00Z',
          task_count: 25,
          completed_task_count: 25,
          overdue_task_count: 0,
          team_member_count: 8
        }
      ]

      projects.value = mockProjects
      pagination.value = {
        page,
        pageSize,
        total: mockProjects.length,
        hasNext: false,
        hasPrevious: false
      }

      return {
        results: mockProjects,
        count: mockProjects.length,
        next: null,
        previous: null
      }
    } catch (err: any) {
      error.value = err.message || '获取项目列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchProject = async (id: string) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用 - 从现有项目列表中查找
      const project = projects.value.find(p => p.id === id)
      if (project) {
        currentProject.value = project
        return project
      } else {
        throw new Error('项目不存在')
      }
    } catch (err: any) {
      error.value = err.message || '获取项目详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createProject = async (data: ProjectCreateRequest) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用 - 创建项目
      const newProject: Project = {
        id: `project-${Date.now()}`,
        name: data.name,
        code: data.code || `PRJ-${Date.now()}`,
        description: data.description || '',
        status: data.status || 'planning',
        priority: data.priority || 'medium',
        start_date: data.start_date || new Date().toISOString().split('T')[0],
        end_date: data.end_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        progress: 0,
        budget: data.budget || 0,
        spent: 0,
        manager_id: data.manager_id || '1',
        manager_name: '管理员',
        team_members: [],
        client_name: data.client_name || '',
        client_contact: data.client_contact || '',
        client_email: data.client_email || '',
        tags: data.tags || '',
        category: data.category || '',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        task_count: 0,
        completed_task_count: 0,
        overdue_task_count: 0,
        team_member_count: 0
      }

      projects.value.unshift(newProject)
      pagination.value.total += 1

      return newProject
    } catch (err: any) {
      error.value = err.message || '创建项目失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateProject = async (id: string, data: Partial<ProjectUpdateRequest>) => {
    loading.value = true
    error.value = null

    try {
      const project = await api.patch<Project>(`/projects/${id}/`, data)

      // 更新列表中的项目
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value[index] = project
      }

      // 更新当前项目
      if (currentProject.value?.id === id) {
        currentProject.value = project
      }

      return project
    } catch (err: any) {
      error.value = err.message || '更新项目失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteProject = async (id: string) => {
    loading.value = true
    error.value = null

    try {
      await api.delete(`/projects/${id}/`)

      // 从列表中移除
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value.splice(index, 1)
        pagination.value.total -= 1
      }

      // 清除当前项目
      if (currentProject.value?.id === id) {
        currentProject.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除项目失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchStats = async () => {
    try {
      const response = await api.get<ProjectStats>('/projects/stats/')
      stats.value = response
      return response
    } catch (err: any) {
      error.value = err.message || '获取统计数据失败'
      throw err
    }
  }

  const updateProgress = async (id: string, progress: number) => {
    try {
      await api.post(`/projects/${id}/update_progress/`, { progress })

      // 更新本地数据
      const project = projects.value.find(p => p.id === id)
      if (project) {
        project.progress = progress
      }

      if (currentProject.value?.id === id) {
        currentProject.value.progress = progress
      }
    } catch (err: any) {
      error.value = err.message || '更新进度失败'
      throw err
    }
  }

  const addMember = async (projectId: string, userId: string, role = '成员') => {
    try {
      await api.post(`/projects/${projectId}/add_member/`, {
        user_id: userId,
        role
      })

      // 重新获取项目详情以更新成员列表
      if (currentProject.value?.id === projectId) {
        await fetchProject(projectId)
      }
    } catch (err: any) {
      error.value = err.message || '添加成员失败'
      throw err
    }
  }

  const removeMember = async (projectId: string, userId: string) => {
    try {
      await api.delete(`/projects/${projectId}/remove_member/`, {
        data: { user_id: userId }
      })

      // 重新获取项目详情以更新成员列表
      if (currentProject.value?.id === projectId) {
        await fetchProject(projectId)
      }
    } catch (err: any) {
      error.value = err.message || '移除成员失败'
      throw err
    }
  }

  const fetchGanttData = async (projectId: string) => {
    try {
      const response = await api.get<GanttData>(`/projects/${projectId}/gantt_data/`)
      return response
    } catch (err: any) {
      error.value = err.message || '获取甘特图数据失败'
      throw err
    }
  }

  const setFilters = (newFilters: Partial<ProjectFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {}
  }

  const setCurrentProject = (project: Project | null) => {
    currentProject.value = project
  }

  const clearError = () => {
    error.value = null
  }

  // 重置状态
  const reset = () => {
    projects.value = []
    currentProject.value = null
    stats.value = null
    loading.value = false
    error.value = null
    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0,
      hasNext: false,
      hasPrevious: false
    }
    filters.value = {}
  }

  return {
    // 状态
    projects: readonly(projects),
    currentProject: readonly(currentProject),
    stats: readonly(stats),
    loading: readonly(loading),
    error: readonly(error),
    pagination: readonly(pagination),
    filters: readonly(filters),

    // 计算属性
    activeProjects,
    completedProjects,
    overdueProjects,
    projectsByPriority,
    totalBudget,
    totalSpent,
    averageProgress,

    // 方法
    fetchProjects,
    fetchProject,
    createProject,
    updateProject,
    deleteProject,
    fetchStats,
    updateProgress,
    addMember,
    removeMember,
    fetchGanttData,
    setFilters,
    clearFilters,
    setCurrentProject,
    clearError,
    reset
  }
})
