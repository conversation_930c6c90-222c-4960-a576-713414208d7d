/**
 * 用户状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiClient from '@/utils/axios'
import type {
  User,
  UserCreateRequest,
  UserUpdateRequest,
  UserFilter,
  UserSort,
  LoginRequest,
  LoginResponse,
  PasswordChangeRequest,
  Team,
  UserStatistics
} from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const users = ref<User[]>([])
  const teams = ref<Team[]>([])
  const statistics = ref<UserStatistics | null>(null)

  const loading = ref(false)
  const error = ref<string | null>(null)
  const isAuthenticated = ref(false)

  // 分页和筛选
  const currentPage = ref(1)
  const pageSize = ref(20)
  const totalCount = ref(0)
  const filter = ref<UserFilter>({})
  const sort = ref<UserSort>({ field: 'full_name', direction: 'asc' })

  // 计算属性
  const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))

  const activeUsers = computed(() => {
    return users.value.filter(user => user.is_active)
  })

  const usersByRole = computed(() => {
    return users.value.reduce((acc, user) => {
      if (!acc[user.role]) {
        acc[user.role] = []
      }
      acc[user.role].push(user)
      return acc
    }, {} as Record<string, User[]>)
  })

  const usersByDepartment = computed(() => {
    return users.value.reduce((acc, user) => {
      const dept = user.department || '未分配'
      if (!acc[dept]) {
        acc[dept] = []
      }
      acc[dept].push(user)
      return acc
    }, {} as Record<string, User[]>)
  })

  // 认证方法
  const login = async (credentials: LoginRequest): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      const response = await apiClient.post<LoginResponse>('/auth/login/', credentials)
      const { access_token, refresh_token, user } = response.data

      // 保存token
      localStorage.setItem('access_token', access_token)
      localStorage.setItem('refresh_token', refresh_token)

      // 设置用户信息
      currentUser.value = user
      isAuthenticated.value = true

      // 设置axios默认header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${access_token}`

      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || '登录失败'
      return false
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      await apiClient.post('/auth/logout/')
    } catch (err) {
      // 忽略登出错误
    } finally {
      // 清除本地数据
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      delete apiClient.defaults.headers.common['Authorization']

      currentUser.value = null
      isAuthenticated.value = false
    }
  }

  const refreshToken = async (): Promise<boolean> => {
    const refresh = localStorage.getItem('refresh_token')
    if (!refresh) return false

    try {
      const response = await apiClient.post('/auth/refresh/', {
        refresh_token: refresh
      })

      const { access_token } = response.data
      localStorage.setItem('access_token', access_token)
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${access_token}`

      return true
    } catch (err) {
      await logout()
      return false
    }
  }

  const checkAuth = async (): Promise<boolean> => {
    const token = localStorage.getItem('access_token')
    if (!token) return false

    try {
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`
      const response = await apiClient.get<User>('/auth/me/')
      currentUser.value = response.data
      isAuthenticated.value = true
      return true
    } catch (err) {
      // Token可能过期，尝试刷新
      return await refreshToken()
    }
  }

  // 用户管理方法
  const fetchUsers = async () => {
    loading.value = true
    error.value = null

    try {
      const params = {
        page: currentPage.value,
        page_size: pageSize.value,
        ...filter.value,
        ordering: sort.value.direction === 'desc' ? `-${sort.value.field}` : sort.value.field
      }

      const response = await apiClient.get('/users/', { params })
      users.value = response.data.results
      totalCount.value = response.data.count
    } catch (err: any) {
      error.value = err.response?.data?.message || '获取用户列表失败'
    } finally {
      loading.value = false
    }
  }

  const fetchUser = async (userId: string): Promise<User | null> => {
    try {
      const response = await apiClient.get<User>(`/users/${userId}/`)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '获取用户信息失败'
      return null
    }
  }

  const createUser = async (userData: UserCreateRequest): Promise<User | null> => {
    loading.value = true
    error.value = null

    try {
      const response = await apiClient.post<User>('/users/', userData)
      const newUser = response.data
      users.value.push(newUser)
      return newUser
    } catch (err: any) {
      error.value = err.response?.data?.message || '创建用户失败'
      return null
    } finally {
      loading.value = false
    }
  }

  const updateUser = async (userId: string, userData: UserUpdateRequest): Promise<User | null> => {
    loading.value = true
    error.value = null

    try {
      const response = await apiClient.patch<User>(`/users/${userId}/`, userData)
      const updatedUser = response.data

      // 更新列表中的用户
      const index = users.value.findIndex(u => u.id === userId)
      if (index > -1) {
        users.value[index] = updatedUser
      }

      // 如果是当前用户，更新当前用户信息
      if (currentUser.value?.id === userId) {
        currentUser.value = updatedUser
      }

      return updatedUser
    } catch (err: any) {
      error.value = err.response?.data?.message || '更新用户失败'
      return null
    } finally {
      loading.value = false
    }
  }

  const deleteUser = async (userId: string): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      await apiClient.delete(`/users/${userId}/`)

      // 从列表中移除用户
      const index = users.value.findIndex(u => u.id === userId)
      if (index > -1) {
        users.value.splice(index, 1)
      }

      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || '删除用户失败'
      return false
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (passwordData: PasswordChangeRequest): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      await apiClient.post('/auth/change-password/', passwordData)
      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || '修改密码失败'
      return false
    } finally {
      loading.value = false
    }
  }

  // 团队管理方法
  const fetchTeams = async () => {
    try {
      const response = await apiClient.get('/teams/')
      teams.value = response.data.results
    } catch (err: any) {
      error.value = err.response?.data?.message || '获取团队列表失败'
    }
  }

  const fetchTeam = async (teamId: string): Promise<Team | null> => {
    try {
      const response = await apiClient.get<Team>(`/teams/${teamId}/`)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '获取团队信息失败'
      return null
    }
  }

  // 统计方法
  const fetchStatistics = async () => {
    try {
      const response = await apiClient.get<UserStatistics>('/users/statistics/')
      statistics.value = response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '获取统计数据失败'
    }
  }

  // 筛选和排序方法
  const setFilter = (newFilter: Partial<UserFilter>) => {
    filter.value = { ...filter.value, ...newFilter }
    currentPage.value = 1
    fetchUsers()
  }

  const setSort = (newSort: UserSort) => {
    sort.value = newSort
    fetchUsers()
  }

  const setPage = (page: number) => {
    currentPage.value = page
    fetchUsers()
  }

  const setPageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    fetchUsers()
  }

  const clearFilter = () => {
    filter.value = {}
    currentPage.value = 1
    fetchUsers()
  }

  // 搜索方法
  const searchUsers = async (query: string) => {
    setFilter({ search: query })
  }

  // 工具方法
  const getUserById = (userId: string): User | undefined => {
    return users.value.find(user => user.id === userId)
  }

  const getUsersByRole = (role: string): User[] => {
    return users.value.filter(user => user.role === role)
  }

  const getUsersByDepartment = (department: string): User[] => {
    return users.value.filter(user => user.department === department)
  }

  // 初始化方法
  const init = async () => {
    await checkAuth()
    if (isAuthenticated.value) {
      await Promise.all([
        fetchUsers(),
        fetchTeams(),
        fetchStatistics()
      ])
    }
  }

  return {
    // 状态
    currentUser,
    users,
    teams,
    statistics,
    loading,
    error,
    isAuthenticated,

    // 分页和筛选
    currentPage,
    pageSize,
    totalCount,
    totalPages,
    filter,
    sort,

    // 计算属性
    activeUsers,
    usersByRole,
    usersByDepartment,

    // 认证方法
    login,
    logout,
    refreshToken,
    checkAuth,

    // 用户管理方法
    fetchUsers,
    fetchUser,
    createUser,
    updateUser,
    deleteUser,
    changePassword,

    // 团队管理方法
    fetchTeams,
    fetchTeam,

    // 统计方法
    fetchStatistics,

    // 筛选和排序方法
    setFilter,
    setSort,
    setPage,
    setPageSize,
    clearFilter,
    searchUsers,

    // 工具方法
    getUserById,
    getUsersByRole,
    getUsersByDepartment,

    // 初始化
    init
  }
})
