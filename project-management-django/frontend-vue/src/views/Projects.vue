<template>
  <v-container fluid>
    <!-- 页面标题和操作 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center">
          <div>
            <h1 class="text-h4 font-weight-bold">项目管理</h1>
            <p class="text-body-1 text-medium-emphasis mt-1">
              管理和跟踪所有项目进度
            </p>
          </div>
          <v-btn
            color="primary"
            prepend-icon="mdi-plus"
            to="/projects/create"
            size="large"
          >
            新建项目
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- 项目统计卡片 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="blue-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-folder-multiple" size="large" color="blue"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ activeProjects.length }}</div>
            <div class="text-body-2">活跃项目</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="green-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-check-circle" size="large" color="green"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ completedProjects.length }}</div>
            <div class="text-body-2">已完成</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="orange-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-alert-circle" size="large" color="orange"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ overdueProjects.length }}</div>
            <div class="text-body-2">逾期项目</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="purple-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-currency-usd" size="large" color="purple"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ totalBudget.toLocaleString() }}</div>
            <div class="text-body-2">总预算</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 项目列表 -->
    <v-row>
      <v-col cols="12">
        <v-card elevation="2">
          <v-card-title class="d-flex justify-space-between align-center pa-6">
            <span class="text-h6">项目列表</span>
            <v-text-field
              v-model="searchQuery"
              label="搜索项目"
              placeholder="输入项目名称或编号"
              variant="outlined"
              density="compact"
              prepend-inner-icon="mdi-magnify"
              clearable
              style="max-width: 300px"
            ></v-text-field>
          </v-card-title>

          <v-divider></v-divider>

          <v-card-text class="pa-0">
            <v-data-table
              :headers="tableHeaders"
              :items="filteredProjects"
              :loading="loading"
              item-key="id"
              class="elevation-0"
              @click:row="openProject"
            >
              <template #item.name="{ item }">
                <div class="d-flex align-center">
                  <v-icon
                    icon="mdi-folder"
                    :color="getStatusColor(item.status)"
                    class="me-2"
                  ></v-icon>
                  <div>
                    <div class="font-weight-medium">{{ item.name }}</div>
                    <div class="text-caption text-medium-emphasis">{{ item.code }}</div>
                  </div>
                </div>
              </template>

              <template #item.status="{ item }">
                <v-chip
                  :color="getStatusColor(item.status)"
                  variant="tonal"
                  size="small"
                >
                  {{ getStatusText(item.status) }}
                </v-chip>
              </template>

              <template #item.priority="{ item }">
                <v-chip
                  :color="getPriorityColor(item.priority)"
                  variant="tonal"
                  size="small"
                >
                  {{ getPriorityText(item.priority) }}
                </v-chip>
              </template>

              <template #item.progress="{ item }">
                <div class="d-flex align-center">
                  <v-progress-linear
                    :model-value="item.progress"
                    :color="getProgressColor(item.progress)"
                    height="6"
                    class="me-2"
                    style="min-width: 80px"
                  ></v-progress-linear>
                  <span class="text-caption">{{ item.progress }}%</span>
                </div>
              </template>

              <template #item.budget="{ item }">
                <div>
                  <div class="font-weight-medium">¥{{ item.budget.toLocaleString() }}</div>
                  <div class="text-caption text-medium-emphasis">
                    已花费: ¥{{ item.spent.toLocaleString() }}
                  </div>
                </div>
              </template>

              <template #item.end_date="{ item }">
                <div>
                  {{ formatDate(item.end_date) }}
                </div>
              </template>

              <template #item.actions="{ item }">
                <v-menu>
                  <template #activator="{ props }">
                    <v-btn
                      icon="mdi-dots-vertical"
                      variant="text"
                      size="small"
                      v-bind="props"
                    ></v-btn>
                  </template>
                  <v-list>
                    <v-list-item @click="editProject(item)">
                      <v-list-item-title>编辑</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="viewTasks(item)">
                      <v-list-item-title>查看任务</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="deleteProject(item)" class="text-red">
                      <v-list-item-title>删除</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { useNotificationStore } from '@/stores/notification'
import type { Project } from '@/types/project'

const router = useRouter()
const projectStore = useProjectStore()
const notificationStore = useNotificationStore()

// 响应式数据
const searchQuery = ref('')

// 计算属性
const { projects, loading, activeProjects, completedProjects, overdueProjects, totalBudget } = projectStore

const filteredProjects = computed(() => {
  if (!searchQuery.value) return projects

  const query = searchQuery.value.toLowerCase()
  return projects.filter(project =>
    project.name.toLowerCase().includes(query) ||
    project.code.toLowerCase().includes(query) ||
    project.description?.toLowerCase().includes(query)
  )
})

// 表格配置
const tableHeaders = [
  { title: '项目名称', key: 'name', sortable: true },
  { title: '状态', key: 'status', sortable: true },
  { title: '优先级', key: 'priority', sortable: true },
  { title: '进度', key: 'progress', sortable: true },
  { title: '预算', key: 'budget', sortable: true },
  { title: '结束日期', key: 'end_date', sortable: true },
  { title: '操作', key: 'actions', sortable: false }
]

// 方法
const openProject = (event: any, { item }: { item: Project }) => {
  router.push(`/projects/${item.id}`)
}

const editProject = (project: Project) => {
  router.push(`/projects/${project.id}/edit`)
}

const viewTasks = (project: Project) => {
  router.push(`/tasks?project=${project.id}`)
}

const deleteProject = async (project: Project) => {
  if (confirm(`确定要删除项目"${project.name}"吗？`)) {
    try {
      await projectStore.deleteProject(project.id)
      notificationStore.success('删除成功', '项目已删除')
    } catch (error) {
      notificationStore.error('删除失败', '删除项目失败')
    }
  }
}

// 工具函数
const getStatusColor = (status: string) => {
  switch (status) {
    case 'planning': return 'blue'
    case 'in_progress': return 'orange'
    case 'completed': return 'green'
    case 'paused': return 'grey'
    case 'cancelled': return 'red'
    default: return 'grey'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'planning': return '规划中'
    case 'in_progress': return '进行中'
    case 'completed': return '已完成'
    case 'paused': return '已暂停'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'low': return 'green'
    case 'medium': return 'orange'
    case 'high': return 'red'
    case 'urgent': return 'purple'
    default: return 'grey'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'low': return '低'
    case 'medium': return '中'
    case 'high': return '高'
    case 'urgent': return '紧急'
    default: return '未知'
  }
}

const getProgressColor = (progress: number) => {
  if (progress >= 80) return 'green'
  if (progress >= 50) return 'orange'
  return 'blue'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(async () => {
  await projectStore.fetchProjects()
})
</script>
