/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    DependencyDialog: typeof import('./src/components/DependencyDialog.vue')['default']
    FileManager: typeof import('./src/components/FileManager.vue')['default']
    GanttChart: typeof import('./src/components/GanttChart.vue')['default']
    NotificationCenter: typeof import('./src/components/NotificationCenter.vue')['default']
    ProjectHealthChart: typeof import('./src/components/ProjectHealthChart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TaskDetailDialog: typeof import('./src/components/TaskDetailDialog.vue')['default']
    TeamWorkloadChart: typeof import('./src/components/TeamWorkloadChart.vue')['default']
    VAlert: typeof import('vuetify/components')['VAlert']
    VApp: typeof import('vuetify/components')['VApp']
    VAppBar: typeof import('vuetify/components')['VAppBar']
    VAppBarNavIcon: typeof import('vuetify/components')['VAppBarNavIcon']
    VAvatar: typeof import('vuetify/components')['VAvatar']
    VBadge: typeof import('vuetify/components')['VBadge']
    VBtn: typeof import('vuetify/components')['VBtn']
    VBtnGroup: typeof import('vuetify/components')['VBtnGroup']
    VBtnToggle: typeof import('vuetify/components')['VBtnToggle']
    VCard: typeof import('vuetify/components')['VCard']
    VCardActions: typeof import('vuetify/components')['VCardActions']
    VCardText: typeof import('vuetify/components')['VCardText']
    VCardTitle: typeof import('vuetify/components')['VCardTitle']
    VCheckbox: typeof import('vuetify/components')['VCheckbox']
    VChip: typeof import('vuetify/components')['VChip']
    VCol: typeof import('vuetify/components')['VCol']
    VCombobox: typeof import('vuetify/components')['VCombobox']
    VContainer: typeof import('vuetify/components')['VContainer']
    VDataTable: typeof import('vuetify/components')['VDataTable']
    VDialog: typeof import('vuetify/components')['VDialog']
    VDivider: typeof import('vuetify/components')['VDivider']
    VForm: typeof import('vuetify/components')['VForm']
    VIcon: typeof import('vuetify/components')['VIcon']
    VImg: typeof import('vuetify/components')['VImg']
    VList: typeof import('vuetify/components')['VList']
    VListItem: typeof import('vuetify/components')['VListItem']
    VListItemSubtitle: typeof import('vuetify/components')['VListItemSubtitle']
    VListItemTitle: typeof import('vuetify/components')['VListItemTitle']
    VMain: typeof import('vuetify/components')['VMain']
    VMenu: typeof import('vuetify/components')['VMenu']
    VNavigationDrawer: typeof import('vuetify/components')['VNavigationDrawer']
    VOverlay: typeof import('vuetify/components')['VOverlay']
    VProgressCircular: typeof import('vuetify/components')['VProgressCircular']
    VProgressLinear: typeof import('vuetify/components')['VProgressLinear']
    VRow: typeof import('vuetify/components')['VRow']
    VSelect: typeof import('vuetify/components')['VSelect']
    VSlider: typeof import('vuetify/components')['VSlider']
    VSpacer: typeof import('vuetify/components')['VSpacer']
    VSwitch: typeof import('vuetify/components')['VSwitch']
    VTextarea: typeof import('vuetify/components')['VTextarea']
    VTextField: typeof import('vuetify/components')['VTextField']
    VTimeline: typeof import('vuetify/components')['VTimeline']
    VTimelineItem: typeof import('vuetify/components')['VTimelineItem']
    VToolbar: typeof import('vuetify/components')['VToolbar']
    VToolbarTitle: typeof import('vuetify/components')['VToolbarTitle']
  }
}
